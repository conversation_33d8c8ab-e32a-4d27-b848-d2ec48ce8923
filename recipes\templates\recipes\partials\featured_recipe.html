{% if error %}
    <div class="featured-recipe">
        <div class="featured-recipe-image">
            <span>❌ Error</span>
        </div>
        <div class="featured-recipe-info">
            <div class="featured-recipe-title">Unable to Load Recipe</div>
            <div class="featured-recipe-details">{{ error }}</div>
        </div>
    </div>
{% elif featured_recipe %}
    <div class="featured-recipe" onclick="viewRecipe('{{ featured_recipe.source_id }}')">
        <div class="featured-recipe-image">
            {% if featured_recipe.image %}
                <img src="{{ featured_recipe.image }}" alt="{{ featured_recipe.title }}" loading="lazy">
            {% else %}
                <span>🍽️ {{ featured_recipe.title|truncatechars:15 }}</span>
            {% endif %}
        </div>
        <div class="featured-recipe-info">
            <div class="featured-recipe-title">{{ featured_recipe.title }}</div>
            <div class="featured-recipe-details">
                {{ featured_recipe.area|default:"International" }} • {{ featured_recipe.category|default:"Main Course" }}
            </div>
            <p class="featured-recipe-description">
                {% if featured_recipe.instructions %}
                    {{ featured_recipe.instructions|truncatechars:150 }}
                {% else %}
                    A delicious recipe waiting to be discovered!
                {% endif %}
            </p>
        </div>
    </div>
    
    <script>
        function viewRecipe(id) {
            // Get recipe name for search history
            const featuredRecipe = document.querySelector('.featured-recipe');
            const recipeName = featuredRecipe?.querySelector('.featured-recipe-title')?.textContent?.trim();

            // Navigate to recipe detail page
            const recipeUrl = `/recipe/themealdb:${id}/`;

            // Add recipe view to search history with the URL
            if (recipeName && window.searchHistoryManager) {
                window.searchHistoryManager.addRecipeViewEntry(recipeName, 'discovery', recipeUrl);
            }

            window.location.href = recipeUrl;
        }
    </script>
{% else %}
    <div class="featured-recipe">
        <div class="featured-recipe-image">
            <span>🍽️ No Recipe</span>
        </div>
        <div class="featured-recipe-info">
            <div class="featured-recipe-title">No Recipe Available</div>
            <div class="featured-recipe-details">Please try again later</div>
        </div>
    </div>
{% endif %}
