// Sliding drawer UI for Search History on Home Icon
// Binds hover events to display recent searches from localStorage

// Helper functions for formatting
function formatTimeAgo(timestamp) {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return new Date(timestamp).toLocaleDateString();
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

(function() {
    document.addEventListener('DOMContentLoaded', () => {
        const homeIcon = document.querySelector('.sidebar-link .bi-house');
        if (!homeIcon) return;
        const link = homeIcon.closest('.sidebar-link');

        // Create drawer container
        const drawer = document.createElement('div');
        drawer.className = 'search-history-drawer';
        document.body.appendChild(drawer);

        // Open drawer on hover
        link.addEventListener('mouseenter', () => {
            const items = window.searchHistoryManager?.getRecentSearches() || [];
            if (items.length === 0) {
                drawer.innerHTML = '<div class="text-muted p-3">No recent searches</div>';
            } else {
                // Group items by formatted date
                const groups = items.reduce((acc, item) => {
                    const dateKey = new Date(item.date).toLocaleDateString(undefined, { year: 'numeric', month: 'long', day: 'numeric' });
                    if (!acc[dateKey]) acc[dateKey] = [];
                    acc[dateKey].push(item);
                    return acc;
                }, {});
                // Sort dates descending
                const sortedDates = Object.keys(groups).sort((a, b) => new Date(b) - new Date(a));
                let html = '';
                sortedDates.forEach(dateKey => {
                    html += `<div class="search-history-date-group"><h5>${dateKey}</h5><ul>`;
                    groups[dateKey].forEach(item => {
                        const timeAgo = formatTimeAgo(item.timestamp);
                        const searchTypeLabel = item.searchType === 'recipe_view' ? 'Recipe View' :
                                              item.searchType === 'ingredients' ? 'Ingredients' :
                                              item.searchType === 'recipe_name' ? 'Recipe Name' :
                                              item.searchType.charAt(0).toUpperCase() + item.searchType.slice(1);

                        html += `<li class="search-history-item" data-query="${item.query}" data-type="${item.searchType}" data-recipe-id="${item.recipeId || ''}">`
                            <div class="search-history-item-main">
                                <div class="search-history-item-query">${escapeHtml(item.query)}</div>
                                <div class="search-history-item-meta">
                                    <span class="search-history-item-type ${item.searchType}">${searchTypeLabel}</span>
                                    <span class="search-history-item-time">${timeAgo}</span>
                                    ${item.resultCount > 0 ? `<span class="search-history-item-results">${item.resultCount} results</span>` : ''}
                                </div>
                            </div>
                        </li>`;
                    });
                    html += `</ul></div>`;
                });
                drawer.innerHTML = html;
            }
            drawer.classList.add('open');
        });

        // Close drawer when leaving it
        drawer.addEventListener('mouseleave', () => {
            drawer.classList.remove('open');
        });

        // Navigate on click
        drawer.addEventListener('click', (e) => {
            const el = e.target.closest('.search-history-item');
            if (!el) return;
            const query = el.dataset.query;
            const type = el.dataset.type;
            const recipeId = el.dataset.recipeId;

            // Handle recipe views differently - navigate directly to recipe detail page
            if (type === 'recipe_view') {
                if (recipeId && recipeId !== 'null' && recipeId !== '') {
                    // Navigate directly to the recipe detail page
                    window.location.href = `/recipe/${recipeId}/`;
                } else {
                    // Fallback: search for the recipe name
                    const url = new URL(link.href, window.location.origin);
                    url.searchParams.set('search_query', query);
                    url.searchParams.set('search_type', 'recipe_name');
                    window.location.href = url.toString();
                }
                return;
            }

            const url = new URL(link.href, window.location.origin);
            url.searchParams.set('search_query', query);
            url.searchParams.set('search_type', type);
            window.location.href = url.toString();
        });
    });
})();
