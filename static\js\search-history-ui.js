// Hover UI for Search History on Home Icon
// Binds hover events to display recent searches from localStorage

// Helper functions for formatting
function formatTimeAgo(timestamp) {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return new Date(timestamp).toLocaleDateString();
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

(function() {
    document.addEventListener('DOMContentLoaded', () => {
        const homeIcon = document.querySelector('.sidebar-link .bi-house');
        if (!homeIcon) return;
        const link = homeIcon.closest('.sidebar-link');
        const li = link.parentElement;
        li.style.position = 'relative';

        // Create dropdown container
        const dropdown = document.createElement('div');
        dropdown.className = 'search-history-dropdown';
        Object.assign(dropdown.style, {
            position: 'absolute',
            top: '100%',
            left: '0',
            background: 'white',
            minWidth: '320px',
            maxWidth: '400px',
            maxHeight: '500px',
            overflowY: 'auto',
            boxShadow: '0 8px 24px rgba(0,0,0,0.15)',
            borderRadius: '12px',
            border: '1px solid #e5e7eb',
            display: 'none',
            zIndex: '1000',
        });
        li.appendChild(dropdown);

        // Show dropdown on hover
        link.addEventListener('mouseenter', () => {
            const items = window.searchHistoryManager?.getRecentSearches() || [];
            if (items.length === 0) {
                dropdown.innerHTML = '<div class="px-3 py-2 text-muted">No recent searches</div>';
            } else {
                // Group items by formatted date (same as mobile)
                const groups = items.reduce((acc, item) => {
                    const dateKey = new Date(item.date).toLocaleDateString(undefined, { year: 'numeric', month: 'long', day: 'numeric' });
                    if (!acc[dateKey]) acc[dateKey] = [];
                    acc[dateKey].push(item);
                    return acc;
                }, {});

                // Sort dates descending
                const sortedDates = Object.keys(groups).sort((a, b) => new Date(b) - new Date(a));
                let html = '';

                sortedDates.forEach(dateKey => {
                    html += `<div class="search-history-date-group">
                        <h5 style="margin: 0; padding: 8px 12px; font-size: 12px; font-weight: 600; background: #f8f9fa; color: #495057; border-bottom: 1px solid #e9ecef;">${dateKey}</h5>
                        <ul style="margin: 0; padding: 0; list-style: none;">`;

                    groups[dateKey].forEach(item => {
                        const timeAgo = formatTimeAgo(item.timestamp);
                        const searchTypeLabel = item.searchType === 'recipe_view' ? 'Recipe View' :
                                              item.searchType === 'ingredients' ? 'Ingredients' :
                                              item.searchType === 'recipe_name' ? 'Recipe Name' :
                                              item.searchType.charAt(0).toUpperCase() + item.searchType.slice(1);

                        html += `<li class="search-history-item" data-query="${item.query}" data-type="${item.searchType}"
                                    style="padding: 8px 12px; cursor: pointer; border-bottom: 1px solid #f1f3f4; transition: background-color 0.2s;">
                            <div class="history-item-main">
                                <div class="history-item-query">${escapeHtml(item.query)}</div>
                                <div class="history-item-meta">
                                    <span class="history-item-type ${item.searchType}">${searchTypeLabel}</span>
                                    <span class="history-item-time">${timeAgo}</span>
                                    ${item.resultCount > 0 ? `<span class="history-item-results">${item.resultCount} results</span>` : ''}
                                </div>
                            </div>
                        </li>`;
                    });
                    html += `</ul></div>`;
                });

                dropdown.innerHTML = html;

                // Bind click on items
                dropdown.querySelectorAll('.search-history-item').forEach(el => {
                    el.addEventListener('click', () => {
                        const query = el.dataset.query;
                        const type = el.dataset.type;

                        // Handle recipe views differently
                        if (type === 'recipe_view') {
                            // Try to get recipe ID and source from the item data
                            const historyItem = items.find(item => item.query === query && item.searchType === type);
                            const recipeId = historyItem?.filters?.recipeId;
                            const recipeSource = historyItem?.filters?.recipeSource;

                            if (recipeId && recipeSource) {
                                // Direct navigation to recipe detail page
                                window.location.href = `/recipe/${recipeSource}:${recipeId}/`;
                            } else {
                                // Fallback: search for the recipe
                                const url = new URL(link.href, window.location.origin);
                                url.searchParams.set('search_query', query);
                                url.searchParams.set('search_type', 'recipe_name');
                                window.location.href = url.toString();
                            }
                        } else {
                            // Regular search behavior
                            const url = new URL(link.href, window.location.origin);
                            url.searchParams.set('search_query', query);
                            url.searchParams.set('search_type', type);
                            window.location.href = url.toString();
                        }
                    });

                    // Add hover effect
                    el.addEventListener('mouseenter', () => {
                        el.style.backgroundColor = '#f8f9fa';
                    });
                    el.addEventListener('mouseleave', () => {
                        el.style.backgroundColor = '';
                    });
                });
            }
            dropdown.style.display = 'block';
        });
        // Hide on leave
        li.addEventListener('mouseleave', () => {
            dropdown.style.display = 'none';
        });
    });
})();
