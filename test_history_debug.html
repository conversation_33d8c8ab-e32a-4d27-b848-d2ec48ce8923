<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>History Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        button { margin: 5px; padding: 10px; }
        .history-item { margin: 5px 0; padding: 10px; background: #f5f5f5; }
        .log { background: #000; color: #0f0; padding: 10px; font-family: monospace; }
    </style>
</head>
<body>
    <h1>History Functionality Debug Test</h1>
    
    <div class="test-section">
        <h2>Test Search History Manager</h2>
        <button onclick="testSaveSearch()">Save Test Search</button>
        <button onclick="testSaveRecipeView()">Save Test Recipe View</button>
        <button onclick="testSaveRecipeViewWithId()">Save Recipe View with ID</button>
        <button onclick="displayHistory()">Display History</button>
        <button onclick="clearHistory()">Clear History</button>
    </div>
    
    <div class="test-section">
        <h2>History Items</h2>
        <div id="history-display"></div>
    </div>
    
    <div class="test-section">
        <h2>Console Log</h2>
        <div id="log" class="log"></div>
    </div>

    <!-- Include the search history manager -->
    <script src="static/js/search-history.js"></script>
    
    <script>
        // Override console.log to display in our log area
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            const logDiv = document.getElementById('log');
            logDiv.innerHTML += args.join(' ') + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
        };

        function testSaveSearch() {
            console.log('=== Testing Save Search ===');
            window.searchHistoryManager.saveSearchToHistory('chicken pasta', 'ingredients', {}, 15);
            console.log('Search saved successfully');
        }

        function testSaveRecipeView() {
            console.log('=== Testing Save Recipe View (without ID) ===');
            window.searchHistoryManager.addRecipeViewEntry('Chicken Alfredo', 'home');
            console.log('Recipe view saved successfully');
        }

        function testSaveRecipeViewWithId() {
            console.log('=== Testing Save Recipe View (with ID) ===');
            window.searchHistoryManager.addRecipeViewEntry('Beef Stroganoff', 'discovery', '52834', 'themealdb');
            console.log('Recipe view with ID saved successfully');
        }

        function displayHistory() {
            console.log('=== Displaying History ===');
            const history = window.searchHistoryManager.getSearchHistory();
            console.log('Total history items:', history.length);
            
            const displayDiv = document.getElementById('history-display');
            displayDiv.innerHTML = '';
            
            history.forEach((item, index) => {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'history-item';
                itemDiv.innerHTML = `
                    <strong>${index + 1}. ${item.query}</strong><br>
                    Type: ${item.searchType}<br>
                    Source: ${item.filters?.source || 'unknown'}<br>
                    Recipe ID: ${item.filters?.recipeId || 'none'}<br>
                    Recipe Source: ${item.filters?.recipeSource || 'none'}<br>
                    Time: ${new Date(item.timestamp).toLocaleString()}<br>
                    <button onclick="testHistoryClick('${item.query}', '${item.searchType}', '${item.filters?.recipeId || ''}', '${item.filters?.recipeSource || ''}')">Test Click</button>
                `;
                displayDiv.appendChild(itemDiv);
                
                console.log(`Item ${index + 1}:`, item);
            });
        }

        function testHistoryClick(query, searchType, recipeId, recipeSource) {
            console.log('=== Testing History Click ===');
            console.log('Query:', query);
            console.log('Search Type:', searchType);
            console.log('Recipe ID:', recipeId);
            console.log('Recipe Source:', recipeSource);
            
            if (searchType === 'recipe_view') {
                if (recipeId && recipeSource && recipeId !== 'undefined' && recipeSource !== 'undefined') {
                    const recipeUrl = `/recipe/${recipeSource}:${recipeId}/`;
                    console.log('Would navigate to:', recipeUrl);
                    alert(`Would navigate to: ${recipeUrl}`);
                } else {
                    const searchUrl = `/?search_query=${encodeURIComponent(query)}&search_type=recipe_name`;
                    console.log('Would search for:', searchUrl);
                    alert(`Would search for: ${searchUrl}`);
                }
            } else {
                const searchUrl = `/?search_query=${encodeURIComponent(query)}&search_type=${searchType}`;
                console.log('Would search with:', searchUrl);
                alert(`Would search with: ${searchUrl}`);
            }
        }

        function clearHistory() {
            console.log('=== Clearing History ===');
            window.searchHistoryManager.clearHistory();
            displayHistory();
            console.log('History cleared');
        }

        // Initialize display
        window.addEventListener('load', function() {
            console.log('=== History Debug Test Loaded ===');
            console.log('Search History Manager:', window.searchHistoryManager);
            displayHistory();
        });
    </script>
</body>
</html>
