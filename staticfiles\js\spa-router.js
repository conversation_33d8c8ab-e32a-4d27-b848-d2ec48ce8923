/**
 * Single Page Application Router for Recipe Finder
 * Provides client-side routing without page reloads to preserve user state
 */

class SPARouter {
    constructor() {
        this.routes = new Map();
        this.currentRoute = null;
        this.contentContainer = null;
        this.loadingIndicator = null;
        this.init();
    }

    init() {
        console.log('🚀 SPA Router initialized');
        this.setupRoutes();
        this.bindEvents();
        this.handleInitialRoute();
    }

    /**
     * Setup route definitions
     */
    setupRoutes() {
        this.routes.set('/', {
            name: 'home',
            title: 'Recipe Finder - Home',
            contentUrl: '/',
            handler: this.handleHomeRoute.bind(this)
        });

        this.routes.set('/discover/', {
            name: 'discover',
            title: 'Recipe Finder - Discover',
            contentUrl: '/discover/',
            handler: this.handleDiscoverRoute.bind(this)
        });

        this.routes.set('/history/', {
            name: 'history',
            title: 'Recipe Finder - History',
            contentUrl: '/history/',
            handler: this.handleHistoryRoute.bind(this)
        });
    }

    /**
     * Navigate to a route without page reload
     */
    navigate(path, options = {}) {
        const { pushState = true, preserveState = true } = options;

        console.log(`🧭 SPA Navigation to: ${path}`);

        // Emit navigation start event
        document.dispatchEvent(new CustomEvent('spa:navigationStart', {
            detail: { path, preserveState }
        }));

        // Save current page state before navigation
        if (preserveState && window.globalStateManager) {
            window.globalStateManager.saveCurrentPageState();
        }

        const route = this.routes.get(path);
        if (!route) {
            console.error(`Route not found: ${path}`);
            return false;
        }

        // Update browser history
        if (pushState) {
            history.pushState({ path, route: route.name }, route.title, path);
        }

        // Update page title
        document.title = route.title;

        // Execute route handler
        this.currentRoute = route;
        route.handler(route).then(() => {
            // Emit navigation complete event
            document.dispatchEvent(new CustomEvent('spa:navigationComplete', {
                detail: { path, route: route.name }
            }));
        }).catch((error) => {
            console.error('Route handler error:', error);
            this.handleRouteError(error);
        });

        // Track navigation in global state
        if (window.globalStateManager) {
            const fromPage = window.globalStateManager.getCurrentPageType();
            window.globalStateManager.trackNavigation(fromPage, route.name, preserveState);
        }

        return true;
    }

    /**
     * Handle home route
     */
    async handleHomeRoute(route) {
        try {
            this.showLoading();

            // Load home page content
            const content = await this.loadPageContent(route.contentUrl);
            this.updateMainContent(content);

            // Update navigation active states
            this.updateNavigationState('home');

            // Restore state if available
            setTimeout(() => {
                if (window.globalStateManager) {
                    window.globalStateManager.restorePageState();
                }
            }, 100);

            this.hideLoading();
            return Promise.resolve();
        } catch (error) {
            console.error('Error loading home route:', error);
            this.handleRouteError(error);
            return Promise.reject(error);
        }
    }

    /**
     * Handle discover route
     */
    async handleDiscoverRoute(route) {
        try {
            this.showLoading();

            // Load discover page content
            const content = await this.loadPageContent(route.contentUrl);
            this.updateMainContent(content);

            // Update navigation active states
            this.updateNavigationState('discover');

            // Restore state if available
            setTimeout(() => {
                if (window.globalStateManager) {
                    window.globalStateManager.restorePageState();
                }
            }, 100);

            this.hideLoading();
            return Promise.resolve();
        } catch (error) {
            console.error('Error loading discover route:', error);
            this.handleRouteError(error);
            return Promise.reject(error);
        }
    }

    /**
     * Handle history route
     */
    async handleHistoryRoute(route) {
        try {
            this.showLoading();

            // Load history page content
            const content = await this.loadPageContent(route.contentUrl);
            this.updateMainContent(content);

            // Update navigation active states
            this.updateNavigationState('history');

            this.hideLoading();
            return Promise.resolve();
        } catch (error) {
            console.error('Error loading history route:', error);
            this.handleRouteError(error);
            return Promise.reject(error);
        }
    }

    /**
     * Load page content via fetch
     */
    async loadPageContent(url) {
        const response = await fetch(url, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'text/html'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const html = await response.text();
        
        // Extract main content from the response
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        
        // Get the main content area (everything inside the main container)
        const mainContent = doc.querySelector('main') || doc.querySelector('.container') || doc.body;
        
        return mainContent ? mainContent.innerHTML : html;
    }

    /**
     * Update the main content area
     */
    updateMainContent(content) {
        const mainContainer = document.querySelector('main') || document.querySelector('.container');
        if (mainContainer) {
            mainContainer.innerHTML = content;
            
            // Re-initialize any JavaScript components that were in the content
            this.reinitializeComponents();
        }
    }

    /**
     * Re-initialize JavaScript components after content update
     */
    reinitializeComponents() {
        // Re-initialize Alpine.js components
        if (window.Alpine) {
            window.Alpine.initTree(document.body);
        }

        // Re-initialize HTMX if present
        if (window.htmx) {
            window.htmx.process(document.body);
        }

        // Trigger custom event for other components to reinitialize
        document.dispatchEvent(new CustomEvent('spa:contentUpdated', {
            detail: { route: this.currentRoute }
        }));
    }

    /**
     * Update navigation active states
     */
    updateNavigationState(activeRoute) {
        // Update sidebar navigation
        document.querySelectorAll('.sidebar-link').forEach(link => {
            link.classList.remove('active');
        });

        // Update mobile navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        // Add active class to current route
        const sidebarLink = document.querySelector(`.sidebar-link[data-route="${activeRoute}"]`);
        if (sidebarLink) {
            sidebarLink.classList.add('active');
        }

        const mobileNavItem = document.querySelector(`.nav-item[data-page="${activeRoute}"]`);
        if (mobileNavItem) {
            mobileNavItem.classList.add('active');
        }
    }

    /**
     * Show loading indicator
     */
    showLoading() {
        if (!this.loadingIndicator) {
            this.loadingIndicator = document.createElement('div');
            this.loadingIndicator.className = 'spa-loading-indicator';
            this.loadingIndicator.innerHTML = `
                <div class="loading-spinner">
                    <div class="spinner"></div>
                    <span>Loading...</span>
                </div>
            `;
            document.body.appendChild(this.loadingIndicator);
        }
        this.loadingIndicator.style.display = 'flex';
    }

    /**
     * Hide loading indicator
     */
    hideLoading() {
        if (this.loadingIndicator) {
            this.loadingIndicator.style.display = 'none';
        }
    }

    /**
     * Handle route errors
     */
    handleRouteError(error) {
        console.error('Route error:', error);
        this.hideLoading();
        
        // Show error message to user
        const errorContent = `
            <div class="error-container">
                <h2>Oops! Something went wrong</h2>
                <p>We couldn't load this page. Please try again.</p>
                <button onclick="window.spaRouter.navigate('/')" class="btn btn-primary">
                    Go Home
                </button>
            </div>
        `;
        this.updateMainContent(errorContent);
    }

    /**
     * Handle initial route on page load
     */
    handleInitialRoute() {
        const currentPath = window.location.pathname;
        const route = this.routes.get(currentPath);
        
        if (route) {
            // Don't push state for initial load
            this.navigate(currentPath, { pushState: false });
        } else {
            // Fallback to home if route not found
            this.navigate('/', { pushState: false });
        }
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Handle browser back/forward buttons
        window.addEventListener('popstate', (event) => {
            const path = event.state?.path || window.location.pathname;
            this.navigate(path, { pushState: false });
        });

        // Handle navigation link clicks
        document.addEventListener('click', (event) => {
            const link = event.target.closest('a[data-spa-route]');
            if (link) {
                event.preventDefault();
                const path = link.getAttribute('data-spa-route');
                this.navigate(path);
            }
        });
    }

    /**
     * Force reload (for "New" button)
     */
    forceReload(path = '/') {
        console.log('🔄 Force reload triggered');
        
        // Clear all state
        if (window.globalStateManager) {
            window.globalStateManager.clearState();
        }
        
        // Perform full page reload
        window.location.href = path;
    }
}

// Initialize SPA Router
window.spaRouter = new SPARouter();

// Make it globally available
window.SPARouter = SPARouter;
